name: venta_cuba
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.10+1

environment:
  sdk: ">=3.0.5 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter_screenutil: ^5.8.4
  get: ^4.6.5
  smooth_page_indicator: ^1.1.0
  pinput: ^3.0.1
  dotted_border: ^2.0.0
  image_picker: ^1.0.1
  flutter_chat_ui: ^1.6.9
  introduction_screen: ^3.1.11
  lottie: ^2.7.0
  fluttertoast: ^8.2.2
  shared_preferences: ^2.2.2
  country_list_pick: ^1.0.0+3
  cached_network_image: ^3.3.0
  url_launcher: ^6.3.1
  permission_handler: ^11.0.1
  file_picker: ^8.1.0
  flutter_rating_bar: ^4.0.1
  image_gallery_saver_plus: ^4.0.1
  flutter_slidable: ^3.1.2
  image: any
  textfield_tags: ^2.0.2
  dropdown_button2: ^2.3.9
  photo_view: ^0.14.0
  path_provider: ^2.1.5
  share_plus: any
  video_player: ^2.9.2
  # Location
  # geocoding: ^3.0.0

  # Supabase
  supabase_flutter: ^2.8.0
  intl: ^0.20.2

  # Push Notifications - OneSignal
  onesignal_flutter: ^5.2.5

  # Local Notifications (keep for local notifications)
  flutter_local_notifications: ^18.0.1

  # GOOGLE MAPS
  google_maps_flutter: ^2.10.0
  geolocator: ^13.0.2
  flutter_google_places_hoc081098: ^2.0.0
  # flutter_google_places: ^0.3.0
  easy_image_viewer: ^1.5.1
  twilio_flutter:
  flutter_app_badge_control: ^0.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_svg: 2.0.9

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/video/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
