import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../services/supabase_chat_service.dart';
import '../../../services/onesignal_service.dart';
import 'package:venta_cuba/Controllers/auth_controller.dart';

class ChatController extends GetxController {
  String? path;
  bool isLast = false;
  bool isTyping = false;
  bool isImageSend = false;

  Stream<List<Map<String, dynamic>>>? chats;
  TextEditingController messageController = TextEditingController();
  ScrollController scrollController = ScrollController();
  bool isShow = false;

  final SupabaseChatService _chatService = SupabaseChatService();
  final OneSignalService _oneSignalService = OneSignalService();

  Future getAllUser() async {
    return _chatService.getChatStream();
  }

  getChats(String id) async {
    return _chatService.getMessagesStream(id);
  }

  Future<void> deleteChat(String docId) async {
    await _chatService.deleteChat(docId);
  }

  Future sendMessage(String userAndPostId, Map<String, dynamic> chatMessageData) async {
    await _chatService.sendMessage(userAndPostId, chatMessageData);
    
    // Update unread count after sending message
    Future.delayed(Duration(milliseconds: 500), () {
      updateUnreadMessageIndicators();
    });
  }

  updateImage(String userAndPostId, Map<String, dynamic> imageData) async {
    await _chatService.updateChatData(userAndPostId, imageData);
  }

  // Mark chat as read for the current user
  Future<void> markChatAsRead(String chatId, String userId) async {
    await _chatService.markChatAsRead(chatId, userId);
    
    // Update unread indicators after marking as read
    Future.delayed(Duration(milliseconds: 300), () {
      updateUnreadMessageIndicators();
    });
  }

  // Get total unread message count for badge
  Future<int> getTotalUnreadMessageCount(String currentUserId) async {
    return await _chatService.getTotalUnreadMessageCount(currentUserId);
  }

  // Get unread message count for a specific chat
  Future<int> getUnreadMessageCountForChat(String chatId, String currentUserId) async {
    return await _chatService.getUnreadMessageCountForChat(chatId, currentUserId);
  }

  // Check if a chat has unread messages
  Future<bool> hasUnreadMessages(String chatId, String currentUserId) async {
    return await _chatService.hasUnreadMessages(chatId, currentUserId);
  }

  // Set user presence (online/offline)
  Future<void> setUserPresence(String userId, bool isOnline) async {
    await _chatService.setUserPresence(userId, isOnline);
  }

  // Get user presence data
  Stream<Map<String, dynamic>?> getUserPresence(String userId) {
    return _chatService.getUserPresenceStream(userId);
  }

  // Update badge count and unread indicators
  Future<void> updateUnreadMessageIndicators() async {
    try {
      final authCont = Get.find<AuthController>();
      String? currentUserId = authCont.user?.userId;
      
      if (currentUserId == null) return;

      int totalUnreadCount = await getTotalUnreadMessageCount(currentUserId);
      
      // Update OneSignal badge count
      await _oneSignalService.setBadgeCount(totalUnreadCount);
      
      print("🔥 📱 Updated badge count: $totalUnreadCount");
      
      // Trigger UI update
      update();
    } catch (e) {
      print("🔥 ❌ Error updating unread message indicators: $e");
    }
  }

  StreamSubscription<List<Map<String, dynamic>>>? _chatListener;

  // Start listening for real-time chat updates
  void startListeningForChatUpdates() {
    try {
      final authCont = Get.find<AuthController>();
      String? currentUserId = authCont.user?.userId;
      
      if (currentUserId == null) return;

      // Listen for changes in chat collection
      _chatListener = _chatService.getChatStream().listen((chats) {
        print("🔥 📱 Chat collection changed - updating badge count");
        // Update both badge count and unread indicators whenever chat data changes
        updateBadgeCountFromChats();
      });
    } catch (e) {
      print("🔥 ❌ Error starting chat listener: $e");
    }
  }

  // Stop listening for chat updates
  void stopListeningForChatUpdates() {
    _chatListener?.cancel();
    _chatListener = null;
  }

  @override
  void onClose() {
    stopListeningForChatUpdates();
    super.onClose();
  }

  // Format last active time for display
  String formatLastActiveTime(DateTime? lastActiveTime) {
    if (lastActiveTime == null) return "Last seen long ago".tr;

    DateTime now = DateTime.now();
    Duration difference = now.difference(lastActiveTime);

    if (difference.inMinutes < 1) {
      return "Online".tr;
    } else if (difference.inMinutes < 60) {
      return "${difference.inMinutes} ${"minutes ago".tr}";
    } else if (difference.inHours < 24) {
      return "${difference.inHours} ${"hours ago".tr}";
    } else {
      return "${difference.inDays} ${"days ago".tr}";
    }
  }

  // Check if user is currently online
  bool isUserOnline(Map<String, dynamic>? presenceData) {
    if (presenceData == null) return false;

    bool isOnline = presenceData['is_online'] ?? false;
    DateTime? lastActiveTime = presenceData['last_seen'] != null 
        ? DateTime.parse(presenceData['last_seen']) 
        : null;

    if (!isOnline) return false;

    // Consider user online if last active within 5 minutes
    if (lastActiveTime != null) {
      Duration difference = DateTime.now().difference(lastActiveTime);
      return difference.inMinutes <= 5;
    }

    return isOnline;
  }

  // Update device token in chat documents
  Future<void> updateDeviceTokenInChat(String chatId, String userId, String newDeviceToken) async {
    await _chatService.updateDeviceTokenInChat(chatId, userId, newDeviceToken);
  }

  // Update badge count from current chat data
  Future<void> updateBadgeCountFromChats() async {
    await updateUnreadMessageIndicators();
  }

  // Create a new chat room
  Future<void> createChatRoom(String chatRoomId, Map<String, dynamic> chatRoomMap) async {
    await _chatService.createChat(chatRoomId, chatRoomMap);
  }

  // Add conversation messages
  Future<void> addConversationMessages(String chatRoomId, Map<String, dynamic> messageMap) async {
    await _chatService.sendMessage(chatRoomId, messageMap);
  }

  // Get all chats for a user
  Stream<List<Map<String, dynamic>>> getAllChatsForUser(String userId) {
    return _chatService.getChatsForUser(userId);
  }

  // Search chats
  Future<List<Map<String, dynamic>>> searchChats(String query, String userId) async {
    return await _chatService.searchChats(query, userId);
  }

  // Delete all messages in a chat
  Future<void> deleteAllMessagesInChat(String chatId) async {
    await _chatService.deleteAllMessagesInChat(chatId);
  }

  // Get chat info
  Future<Map<String, dynamic>?> getChatInfo(String chatId) async {
    return await _chatService.getChatInfo(chatId);
  }

  // Update chat info
  Future<void> updateChatInfo(String chatId, Map<String, dynamic> updateData) async {
    await _chatService.updateChatData(chatId, updateData);
  }

  // Block/unblock user
  Future<void> blockUser(String chatId, String userId, bool isBlocked) async {
    await _chatService.updateChatData(chatId, {
      'is_blocked': isBlocked,
      'blocked_by': isBlocked ? userId : null,
    });
  }

  // Check if chat is blocked
  Future<bool> isChatBlocked(String chatId) async {
    final chatInfo = await getChatInfo(chatId);
    return chatInfo?['is_blocked'] ?? false;
  }

  // Get message count for a chat
  Future<int> getMessageCount(String chatId) async {
    return await _chatService.getMessageCount(chatId);
  }

  // Mark all chats as read for user
  Future<void> markAllChatsAsRead(String userId) async {
    await _chatService.markAllChatsAsRead(userId);
    await updateUnreadMessageIndicators();
  }
}
