import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import 'dart:convert';
import '../Models/user_data.dart';
import '../Utils/funcations.dart';
import '../view/Navigation bar/navigation_bar.dart';
import '../view/auth/login.dart';

class SupabaseAuthService {
  static final SupabaseAuthService _instance = SupabaseAuthService._internal();
  factory SupabaseAuthService() => _instance;
  SupabaseAuthService._internal();

  SupabaseClient get _client => Supabase.instance.client;
  
  // Get current user
  User? get currentUser => _client.auth.currentUser;
  
  // Check if user is logged in
  bool get isLoggedIn => currentUser != null;

  // Sign up with email and password
  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String province,
    required String city,
  }) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: {
          'first_name': firstName,
          'last_name': lastName,
          'province': province,
          'city': city,
        },
      );
      
      if (response.user != null) {
        // Create user profile in your custom table
        await _createUserProfile(
          userId: response.user!.id,
          email: email,
          firstName: firstName,
          lastName: lastName,
          province: province,
          city: city,
        );
      }
      
      return response;
    } catch (e) {
      print('Supabase signup error: $e');
      rethrow;
    }
  }

  // Sign in with email and password
  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user != null) {
        await _saveUserSession(response);
      }
      
      return response;
    } catch (e) {
      print('Supabase signin error: $e');
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
      await _clearUserSession();
    } catch (e) {
      print('Supabase signout error: $e');
      rethrow;
    }
  }

  // Send password reset email
  Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      print('Supabase password reset error: $e');
      rethrow;
    }
  }

  // Update password
  Future<UserResponse> updatePassword(String newPassword) async {
    try {
      final response = await _client.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      return response;
    } catch (e) {
      print('Supabase update password error: $e');
      rethrow;
    }
  }

  // Send OTP to phone number
  Future<void> sendOTP(String phoneNumber) async {
    try {
      await _client.auth.signInWithOtp(
        phone: phoneNumber,
      );
    } catch (e) {
      print('Supabase send OTP error: $e');
      rethrow;
    }
  }

  // Verify OTP
  Future<AuthResponse> verifyOTP({
    required String phoneNumber,
    required String otp,
  }) async {
    try {
      final response = await _client.auth.verifyOTP(
        type: OtpType.sms,
        token: otp,
        phone: phoneNumber,
      );
      
      if (response.user != null) {
        await _saveUserSession(response);
      }
      
      return response;
    } catch (e) {
      print('Supabase verify OTP error: $e');
      rethrow;
    }
  }

  // Get user profile from custom table
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      final response = await _client
          .from('user_profiles')
          .select()
          .eq('user_id', userId)
          .single();
      
      return response;
    } catch (e) {
      print('Error getting user profile: $e');
      return null;
    }
  }

  // Update user profile
  Future<void> updateUserProfile({
    required String userId,
    required Map<String, dynamic> updates,
  }) async {
    try {
      await _client
          .from('user_profiles')
          .update(updates)
          .eq('user_id', userId);
    } catch (e) {
      print('Error updating user profile: $e');
      rethrow;
    }
  }

  // Delete user account
  Future<void> deleteAccount() async {
    try {
      if (currentUser != null) {
        // Delete user profile from custom table
        await _client
            .from('user_profiles')
            .delete()
            .eq('user_id', currentUser!.id);
        
        // Note: Supabase doesn't have a direct delete user method from client
        // You'll need to implement this via a server function or admin API
        await _client.auth.signOut();
        await _clearUserSession();
      }
    } catch (e) {
      print('Error deleting account: $e');
      rethrow;
    }
  }

  // Listen to auth state changes
  void listenToAuthChanges() {
    _client.auth.onAuthStateChange.listen((data) {
      final event = data.event;
      final user = data.session?.user;
      
      switch (event) {
        case AuthChangeEvent.signedIn:
          print('User signed in: ${user?.email}');
          break;
        case AuthChangeEvent.signedOut:
          print('User signed out');
          _clearUserSession();
          Get.offAll(() => const Login());
          break;
        case AuthChangeEvent.tokenRefreshed:
          print('Token refreshed');
          break;
        default:
          break;
      }
    });
  }

  // Private helper methods
  Future<void> _createUserProfile({
    required String userId,
    required String email,
    required String firstName,
    required String lastName,
    required String province,
    required String city,
  }) async {
    try {
      await _client.from('user_profiles').insert({
        'user_id': userId,
        'email': email,
        'first_name': firstName,
        'last_name': lastName,
        'province': province,
        'city': city,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error creating user profile: $e');
      rethrow;
    }
  }

  Future<void> _saveUserSession(AuthResponse response) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (response.session != null) {
        await prefs.setString('supabase_session', jsonEncode({
          'access_token': response.session!.accessToken,
          'refresh_token': response.session!.refreshToken,
          'user_id': response.user!.id,
          'email': response.user!.email,
        }));
      }
      
      // Get and save user profile data
      if (response.user != null) {
        final profile = await getUserProfile(response.user!.id);
        if (profile != null) {
          await prefs.setString('user_data', jsonEncode(profile));
        }
      }
    } catch (e) {
      print('Error saving user session: $e');
    }
  }

  Future<void> _clearUserSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('supabase_session');
      await prefs.remove('user_data');
      await prefs.remove('token');
    } catch (e) {
      print('Error clearing user session: $e');
    }
  }

  // Check if user session is valid
  Future<bool> isSessionValid() async {
    try {
      final session = _client.auth.currentSession;
      return session != null && !session.isExpired;
    } catch (e) {
      print('Error checking session validity: $e');
      return false;
    }
  }

  // Restore session from storage
  Future<void> restoreSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionData = prefs.getString('supabase_session');
      
      if (sessionData != null) {
        final session = jsonDecode(sessionData);
        // Supabase automatically handles session restoration
        // Just check if current session is valid
        if (await isSessionValid()) {
          print('Session restored successfully');
        } else {
          await _clearUserSession();
        }
      }
    } catch (e) {
      print('Error restoring session: $e');
      await _clearUserSession();
    }
  }
}
