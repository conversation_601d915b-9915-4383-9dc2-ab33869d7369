import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:get/get.dart';
import 'dart:async';

class SupabaseChatService {
  static final SupabaseChatService _instance = SupabaseChatService._internal();
  factory SupabaseChatService() => _instance;
  SupabaseChatService._internal();

  SupabaseClient get _client => Supabase.instance.client;
  
  // Stream controllers for real-time updates
  final Map<String, StreamController<List<Map<String, dynamic>>>> _messageStreams = {};
  final Map<String, StreamSubscription> _subscriptions = {};

  // Get all chats for a user
  Future<List<Map<String, dynamic>>> getUserChats(String userId) async {
    try {
      final response = await _client
          .from('chats')
          .select('''
            *,
            messages:messages(*)
          ''')
          .or('sender_id.eq.$userId,recipient_id.eq.$userId')
          .order('updated_at', ascending: false);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Error getting user chats: $e');
      return [];
    }
  }

  // Get messages for a specific chat
  Stream<List<Map<String, dynamic>>> getChatMessages(String chatId) {
    if (!_messageStreams.containsKey(chatId)) {
      _messageStreams[chatId] = StreamController<List<Map<String, dynamic>>>.broadcast();
      
      // Subscribe to real-time updates
      _subscriptions[chatId] = _client
          .from('messages')
          .stream(primaryKey: ['id'])
          .eq('chat_id', chatId)
          .order('created_at', ascending: true)
          .listen((data) {
            _messageStreams[chatId]?.add(List<Map<String, dynamic>>.from(data));
          });
    }
    
    return _messageStreams[chatId]!.stream;
  }

  // Send a message
  Future<Map<String, dynamic>?> sendMessage({
    required String chatId,
    required String senderId,
    required String recipientId,
    required String message,
    required String messageType,
    String? senderName,
    String? senderDeviceToken,
    String? recipientDeviceToken,
  }) async {
    try {
      // First, ensure the chat exists or create it
      await _ensureChatExists(
        chatId: chatId,
        senderId: senderId,
        recipientId: recipientId,
        senderDeviceToken: senderDeviceToken,
        recipientDeviceToken: recipientDeviceToken,
      );

      // Insert the message
      final messageData = {
        'chat_id': chatId,
        'sender_id': senderId,
        'recipient_id': recipientId,
        'message': message,
        'message_type': messageType,
        'sender_name': senderName,
        'created_at': DateTime.now().toIso8601String(),
        'is_read': false,
      };

      final response = await _client
          .from('messages')
          .insert(messageData)
          .select()
          .single();

      // Update the chat's last message and timestamp
      await _client
          .from('chats')
          .update({
            'last_message': message,
            'last_message_type': messageType,
            'last_message_sender_id': senderId,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', chatId);

      return response;
    } catch (e) {
      print('Error sending message: $e');
      return null;
    }
  }

  // Create or get a chat between two users
  Future<String?> createOrGetChat({
    required String senderId,
    required String recipientId,
    String? senderDeviceToken,
    String? recipientDeviceToken,
  }) async {
    try {
      // Generate a consistent chat ID based on user IDs
      final chatId = _generateChatId(senderId, recipientId);
      
      await _ensureChatExists(
        chatId: chatId,
        senderId: senderId,
        recipientId: recipientId,
        senderDeviceToken: senderDeviceToken,
        recipientDeviceToken: recipientDeviceToken,
      );
      
      return chatId;
    } catch (e) {
      print('Error creating/getting chat: $e');
      return null;
    }
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String chatId, String userId) async {
    try {
      await _client
          .from('messages')
          .update({'is_read': true})
          .eq('chat_id', chatId)
          .eq('recipient_id', userId)
          .eq('is_read', false);
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  // Get unread message count for a user
  Future<int> getUnreadMessageCount(String userId) async {
    try {
      final response = await _client
          .from('messages')
          .select('id')
          .eq('recipient_id', userId)
          .eq('is_read', false);
      
      return response.length;
    } catch (e) {
      print('Error getting unread message count: $e');
      return 0;
    }
  }

  // Update user presence (online/offline)
  Future<void> updateUserPresence(String userId, bool isOnline) async {
    try {
      await _client
          .from('user_presence')
          .upsert({
            'user_id': userId,
            'is_online': isOnline,
            'last_seen': DateTime.now().toIso8601String(),
          });
    } catch (e) {
      print('Error updating user presence: $e');
    }
  }

  // Get user presence
  Future<Map<String, dynamic>?> getUserPresence(String userId) async {
    try {
      final response = await _client
          .from('user_presence')
          .select()
          .eq('user_id', userId)
          .maybeSingle();
      
      return response;
    } catch (e) {
      print('Error getting user presence: $e');
      return null;
    }
  }

  // Update device token in chat
  Future<void> updateDeviceTokenInChats(String userId, String newToken) async {
    try {
      // Update as sender
      await _client
          .from('chats')
          .update({'sender_device_token': newToken})
          .eq('sender_id', userId);

      // Update as recipient
      await _client
          .from('chats')
          .update({'recipient_device_token': newToken})
          .eq('recipient_id', userId);
    } catch (e) {
      print('Error updating device token in chats: $e');
    }
  }

  // Get recipient device token
  Future<String?> getRecipientDeviceToken(String chatId, String currentUserId) async {
    try {
      final response = await _client
          .from('chats')
          .select('sender_id, recipient_id, sender_device_token, recipient_device_token')
          .eq('id', chatId)
          .single();

      if (response['sender_id'] == currentUserId) {
        return response['recipient_device_token'];
      } else {
        return response['sender_device_token'];
      }
    } catch (e) {
      print('Error getting recipient device token: $e');
      return null;
    }
  }

  // Private helper methods
  String _generateChatId(String userId1, String userId2) {
    // Create a consistent chat ID regardless of order
    final sortedIds = [userId1, userId2]..sort();
    return '${sortedIds[0]}_${sortedIds[1]}';
  }

  Future<void> _ensureChatExists({
    required String chatId,
    required String senderId,
    required String recipientId,
    String? senderDeviceToken,
    String? recipientDeviceToken,
  }) async {
    try {
      // Check if chat exists
      final existingChat = await _client
          .from('chats')
          .select('id')
          .eq('id', chatId)
          .maybeSingle();

      if (existingChat == null) {
        // Create new chat
        await _client.from('chats').insert({
          'id': chatId,
          'sender_id': senderId,
          'recipient_id': recipientId,
          'sender_device_token': senderDeviceToken,
          'recipient_device_token': recipientDeviceToken,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      } else {
        // Update device tokens if provided
        final updates = <String, dynamic>{};
        if (senderDeviceToken != null) {
          updates['sender_device_token'] = senderDeviceToken;
        }
        if (recipientDeviceToken != null) {
          updates['recipient_device_token'] = recipientDeviceToken;
        }
        
        if (updates.isNotEmpty) {
          await _client
              .from('chats')
              .update(updates)
              .eq('id', chatId);
        }
      }
    } catch (e) {
      print('Error ensuring chat exists: $e');
      rethrow;
    }
  }

  // Clean up streams and subscriptions
  void dispose() {
    for (final subscription in _subscriptions.values) {
      subscription.cancel();
    }
    for (final stream in _messageStreams.values) {
      stream.close();
    }
    _subscriptions.clear();
    _messageStreams.clear();
  }

  // Stop listening to a specific chat
  void stopListeningToChat(String chatId) {
    _subscriptions[chatId]?.cancel();
    _messageStreams[chatId]?.close();
    _subscriptions.remove(chatId);
    _messageStreams.remove(chatId);
  }
}
