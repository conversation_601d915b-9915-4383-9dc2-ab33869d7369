-- Supabase Database Schema for VentaCuba App
-- This file contains the SQL commands to create the necessary tables for migrating from Firebase

-- Enable Row Level Security (RLS) for all tables
-- Enable real-time subscriptions for chat functionality

-- 1. User Profiles Table (extends Supabase auth.users)
CREATE TABLE user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    email TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    province TEXT,
    city TEXT,
    phone TEXT,
    profile_image TEXT,
    business_logo TEXT,
    device_token TEXT,
    is_business_account BOOLEAN DEFAULT FALSE,
    instagram_link TEXT,
    facebook_link TEXT,
    pinterest_link TEXT,
    twitter_link TEXT,
    linkedin_link TEXT,
    tiktok_link TEXT,
    youtube_link TEXT,
    business_instagram_link TEXT,
    business_facebook_link TEXT,
    business_pinterest_link TEXT,
    business_twitter_link TEXT,
    business_linkedin_link TEXT,
    business_tiktok_link TEXT,
    business_youtube_link TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Chats Table
CREATE TABLE chats (
    id TEXT PRIMARY KEY, -- Format: "userId1_userId2" (sorted)
    sender_id TEXT NOT NULL,
    recipient_id TEXT NOT NULL,
    sender_device_token TEXT,
    recipient_device_token TEXT,
    last_message TEXT,
    last_message_type TEXT DEFAULT 'text',
    last_message_sender_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Messages Table
CREATE TABLE messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    chat_id TEXT REFERENCES chats(id) ON DELETE CASCADE NOT NULL,
    sender_id TEXT NOT NULL,
    recipient_id TEXT NOT NULL,
    message TEXT NOT NULL,
    message_type TEXT DEFAULT 'text', -- 'text', 'image', 'voice'
    sender_name TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    pending BOOLEAN DEFAULT FALSE, -- For temporary messages during upload
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. User Presence Table (for online/offline status)
CREATE TABLE user_presence (
    user_id TEXT PRIMARY KEY,
    is_online BOOLEAN DEFAULT FALSE,
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Categories Table (if needed for local storage)
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    parent_id INTEGER REFERENCES categories(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Listings Table (for posts/listings)
CREATE TABLE listings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'USD',
    category_id INTEGER REFERENCES categories(id),
    sub_category_id INTEGER REFERENCES categories(id),
    sub_sub_category_id INTEGER REFERENCES categories(id),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    address TEXT,
    gallery JSONB, -- Array of image URLs
    additional_features JSONB,
    tags TEXT[],
    status TEXT DEFAULT 'active', -- 'active', 'inactive', 'sold'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_chats_sender_id ON chats(sender_id);
CREATE INDEX idx_chats_recipient_id ON chats(recipient_id);
CREATE INDEX idx_chats_updated_at ON chats(updated_at DESC);
CREATE INDEX idx_messages_chat_id ON messages(chat_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_messages_is_read ON messages(is_read);
CREATE INDEX idx_user_presence_user_id ON user_presence(user_id);
CREATE INDEX idx_listings_user_id ON listings(user_id);
CREATE INDEX idx_listings_status ON listings(status);
CREATE INDEX idx_listings_category_id ON listings(category_id);

-- Row Level Security (RLS) Policies

-- User Profiles: Users can only see and modify their own profile
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Chats: Users can only see chats they participate in
ALTER TABLE chats ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own chats" ON chats FOR SELECT USING (
    auth.uid()::text = sender_id OR auth.uid()::text = recipient_id
);
CREATE POLICY "Users can create chats" ON chats FOR INSERT WITH CHECK (
    auth.uid()::text = sender_id OR auth.uid()::text = recipient_id
);
CREATE POLICY "Users can update own chats" ON chats FOR UPDATE USING (
    auth.uid()::text = sender_id OR auth.uid()::text = recipient_id
);

-- Messages: Users can only see messages in chats they participate in
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own messages" ON messages FOR SELECT USING (
    auth.uid()::text = sender_id OR auth.uid()::text = recipient_id
);
CREATE POLICY "Users can send messages" ON messages FOR INSERT WITH CHECK (
    auth.uid()::text = sender_id
);
CREATE POLICY "Users can update own messages" ON messages FOR UPDATE USING (
    auth.uid()::text = sender_id OR auth.uid()::text = recipient_id
);

-- User Presence: Users can see all presence info but only update their own
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view all presence" ON user_presence FOR SELECT USING (true);
CREATE POLICY "Users can update own presence" ON user_presence FOR INSERT WITH CHECK (auth.uid()::text = user_id);
CREATE POLICY "Users can upsert own presence" ON user_presence FOR UPDATE USING (auth.uid()::text = user_id);

-- Listings: Users can see all listings but only modify their own
ALTER TABLE listings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Anyone can view active listings" ON listings FOR SELECT USING (status = 'active' OR auth.uid() = user_id);
CREATE POLICY "Users can create own listings" ON listings FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own listings" ON listings FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own listings" ON listings FOR DELETE USING (auth.uid() = user_id);

-- Enable real-time subscriptions for chat functionality
ALTER PUBLICATION supabase_realtime ADD TABLE chats;
ALTER PUBLICATION supabase_realtime ADD TABLE messages;
ALTER PUBLICATION supabase_realtime ADD TABLE user_presence;

-- Functions and Triggers

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chats_updated_at BEFORE UPDATE ON chats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_presence_updated_at BEFORE UPDATE ON user_presence FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_listings_updated_at BEFORE UPDATE ON listings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle user profile creation on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (user_id, email, first_name, last_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'last_name', '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
