import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:flutter/foundation.dart';

class OneSignalConfig {
  // Replace with your OneSignal App ID
  static const String appId = 'YOUR_ONESIGNAL_APP_ID';
  
  static Future<void> initialize() async {
    // Remove this method to stop OneSignal Debugging
    if (kDebugMode) {
      OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
    }

    // OneSignal Initialization
    OneSignal.initialize(appId);

    // The promptForPushNotificationsWithUserResponse function will show the iOS or Android push notification prompt.
    // We recommend removing the following code and instead using an In-App Message to prompt for notification permission
    OneSignal.Notifications.requestPermission(true);
  }

  static Future<void> setExternalUserId(String userId) async {
    await OneSignal.login(userId);
  }

  static Future<void> removeExternalUserId() async {
    await OneSignal.logout();
  }

  static Future<String?> getDeviceId() async {
    final deviceState = OneSignal.User.pushSubscription.id;
    return deviceState;
  }

  static void setNotificationWillShowInForegroundHandler() {
    OneSignal.Notifications.addForegroundWillDisplayListener((event) {
      // Will be called whenever a notification is received in foreground
      // Display Notification, pass null param for not displaying the notification
      event.preventDefault();
      event.notification.display();
    });
  }

  static void setNotificationOpenedHandler() {
    OneSignal.Notifications.addClickListener((event) {
      // Will be called whenever a notification is opened/button pressed.
      print('NOTIFICATION CLICK LISTENER CALLED WITH: ${event}');
      // Handle notification click here
    });
  }

  static Future<void> sendTag(String key, String value) async {
    OneSignal.User.addTag(key, value);
  }

  static Future<void> deleteTag(String key) async {
    OneSignal.User.removeTag(key);
  }
}
