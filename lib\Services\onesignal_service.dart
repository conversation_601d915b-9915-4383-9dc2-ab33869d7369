import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_app_badge_control/flutter_app_badge_control.dart';
import 'package:get/get.dart';
import '../Controllers/auth_controller.dart';

class OneSignalService {
  static final OneSignalService _instance = OneSignalService._internal();
  factory OneSignalService() => _instance;
  OneSignalService._internal();

  static int _badgeCount = 0;
  final authCont = Get.find<AuthController>();

  // Initialize OneSignal
  static Future<void> initialize() async {
    if (kDebugMode) {
      OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
    }

    // Replace with your OneSignal App ID
    OneSignal.initialize('YOUR_ONESIGNAL_APP_ID');

    // Request permission for notifications
    OneSignal.Notifications.requestPermission(true);
  }

  // Set up notification handlers
  void setupNotificationHandlers() {
    // Foreground notification handler
    OneSignal.Notifications.addForegroundWillDisplayListener((event) {
      print('OneSignal: notification received in foreground');

      // Increment badge count
      incrementBadgeCount();

      // Set unread messages flag
      authCont.hasUnreadMessages.value = true;
      authCont.update();

      // Display the notification
      event.notification.display();
    });

    // Notification click handler
    OneSignal.Notifications.addClickListener((event) {
      print('OneSignal: notification clicked');
      print('Data: ${event.notification.additionalData}');

      // Handle notification click based on type
      final data = event.notification.additionalData;
      if (data != null) {
        handleNotificationClick(data);
      }
    });
  }

  // Handle notification click actions
  void handleNotificationClick(Map<String, dynamic> data) {
    final type = data['type'];

    switch (type) {
      case 'message':
        // Navigate to chat screen
        final userId = data['userId'];
        final remoteId = data['remoteId'];
        if (userId != null && remoteId != null) {
          // Navigate to specific chat
          // Get.toNamed('/chat', arguments: {'userId': userId, 'remoteId': remoteId});
        }
        break;
      case 'favorite_seller':
        // Navigate to seller's posts or profile
        break;
      default:
        // Handle other notification types
        break;
    }
  }

  // Set external user ID (login user)
  Future<void> setExternalUserId(String userId) async {
    await OneSignal.login(userId);
  }

  // Remove external user ID (logout)
  Future<void> removeExternalUserId() async {
    await OneSignal.logout();
  }

  // Get device push subscription ID
  Future<String?> getDeviceId() async {
    return OneSignal.User.pushSubscription.id;
  }

  // Send tag to OneSignal
  Future<void> sendTag(String key, String value) async {
    OneSignal.User.addTags({key: value});
  }

  // Remove tag from OneSignal
  Future<void> deleteTag(String key) async {
    OneSignal.User.removeTags([key]);
  }

  // Badge count management
  static Future<void> incrementBadgeCount() async {
    _badgeCount++;
    await _saveBadgeCount();
    await _updateAppIconBadge(_badgeCount);
  }

  static Future<void> decrementBadgeCount() async {
    if (_badgeCount > 0) {
      _badgeCount--;
      await _saveBadgeCount();
      await _updateAppIconBadge(_badgeCount);
    }
  }

  static Future<void> resetBadgeCount() async {
    _badgeCount = 0;
    await _saveBadgeCount();
    await _updateAppIconBadge(0);
  }

  static int getBadgeCount() => _badgeCount;

  static Future<void> _saveBadgeCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('badge_count', _badgeCount);
    } catch (e) {
      print('Error saving badge count: $e');
    }
  }

  static Future<void> loadBadgeCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _badgeCount = prefs.getInt('badge_count') ?? 0;
      await _updateAppIconBadge(_badgeCount);
    } catch (e) {
      print('Error loading badge count: $e');
      _badgeCount = 0;
    }
  }

  static Future<void> _updateAppIconBadge(int count) async {
    try {
      if (await FlutterAppBadgeControl.isAppBadgeSupported()) {
        if (count > 0) {
          await FlutterAppBadgeControl.updateBadgeCount(count);
        } else {
          await FlutterAppBadgeControl.removeBadge();
        }
      }
    } catch (e) {
      print('Error updating app badge: $e');
    }
  }

  // Check if badge control is supported
  static Future<bool> isBadgeSupported() async {
    try {
      return await FlutterAppBadgeControl.isAppBadgeSupported();
    } catch (e) {
      print('Error checking badge support: $e');
      return false;
    }
  }
}
